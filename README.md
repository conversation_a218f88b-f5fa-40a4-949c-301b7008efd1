# Mika寄售平台 - 企业级单页网站

## 项目简介

这是一个基于PHP的企业级单页网站，功能与Telegram机器人完全相同，提供完整的在线商城解决方案。

### 主要功能

- 🏪 **商户管理** - 商户注册、登录、信息管理
- 📦 **商品展示** - 商品列表、详情、库存管理
- 🛒 **购物功能** - 商品搜索、购买、订单管理
- 💳 **支付集成** - 支持微信支付、支付宝
- 🚀 **自动发货** - 支付成功后自动发货
- 📱 **响应式设计** - 完美适配PC和移动端
- 🔒 **安全可靠** - 企业级安全防护

## 技术栈

- **后端**: PHP 7.4+
- **前端**: Bootstrap 5.3, JavaScript ES6+
- **服务器**: Nginx
- **API**: RESTful API (复用现有后端)

## 环境要求

- PHP 7.4 或更高版本
- Nginx Web服务器
- 现有的后端API服务 (运行在 http://127.0.0.1:7893/)

## 安装部署

### 1. 文件部署

将以下文件上传到您的Web服务器根目录：

```
/var/www/html/
├── index.php          # 主页面文件
├── config.php         # 配置文件
└── README.md          # 说明文档
```

### 2. Nginx配置

在Nginx配置文件中添加以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    index index.php index.html;

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全配置
    location ~ /\.ht {
        deny all;
    }
}
```

### 3. 配置修改

编辑 `config.php` 文件，根据您的环境修改以下配置：

```php
// API配置 - 修改为您的API服务地址
define('API_BASE_URL', 'http://127.0.0.1:7893/');

// 管理后台URL - 修改为您的管理后台地址
define('ADMIN_BASE_URL', 'https://your-domain.com/admin/');

// 投诉页面URL
define('COMPLAINT_URL', 'https://your-domain.com/tousu.html');
```

### 4. 权限设置

```bash
# 设置文件权限
chmod 644 index.php config.php README.md
chown www-data:www-data index.php config.php

# 重启Nginx和PHP-FPM
systemctl restart nginx
systemctl restart php7.4-fpm
```

## 功能说明

### 首页
- 平台介绍和特色展示
- 快速导航到各个功能模块
- 响应式设计，适配各种设备

### 商城模块
- 商户搜索功能
- 商品列表展示
- 商品详情查看
- 购买流程

### 商户中心
- 商户注册/登录
- 商户信息展示
- 管理后台链接
- 店铺分享功能

### 订单查询
- 订单状态查询
- 支付状态检查
- 发货内容展示
- 投诉功能

## API接口

网站使用以下API接口（与机器人共用）：

- `get_merchant_info.php` - 获取商户信息
- `register_merchant.php` - 注册商户
- `get_product_list.php` - 获取商品列表
- `get_product_info.php` - 获取商品详情
- `create_order.php` - 创建订单
- `check_payment_status.php` - 检查支付状态

## 安全特性

### 输入验证
- 所有用户输入都经过严格验证和过滤
- 防止XSS和SQL注入攻击
- 参数格式验证

### 数据传输
- HTTPS加密传输（生产环境推荐）
- API调用超时控制
- 错误信息安全处理

### 访问控制
- 商户权限验证
- 订单访问控制
- 敏感操作日志记录

## 自定义配置

### 主题定制
可以通过修改CSS变量来自定义主题颜色：

```css
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}
```

### 功能扩展
- 支持添加新的支付方式
- 可扩展商品分类功能
- 支持多语言国际化
- 可集成第三方统计分析

## 性能优化

### 前端优化
- CSS/JS文件压缩
- 图片懒加载
- 浏览器缓存策略
- CDN加速

### 后端优化
- API响应缓存
- 数据库查询优化
- 服务器资源监控
- 负载均衡配置

## 监控和维护

### 日志管理
- 访问日志记录
- 错误日志监控
- 性能指标统计
- 安全事件追踪

### 备份策略
- 定期数据备份
- 配置文件备份
- 灾难恢复计划
- 版本控制管理

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查API服务是否正常运行
   - 验证API_BASE_URL配置是否正确
   - 检查网络连接和防火墙设置

2. **页面显示异常**
   - 检查PHP版本兼容性
   - 验证Nginx配置是否正确
   - 查看浏览器控制台错误信息

3. **支付功能异常**
   - 检查支付接口配置
   - 验证商户密钥设置
   - 查看支付日志记录

### 调试模式
在开发环境中可以启用调试模式：

```php
// 在config.php中添加
define('DEBUG_MODE', true);
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

## 技术支持

如有技术问题，请通过以下方式联系：

- 官方频道: @MikaJiShou8
- 投诉建议: https://cloudshop.qnm6.top/tousu.html

## 版本历史

### v1.0.0 (2024-08-03)
- 初始版本发布
- 完整的商城功能
- 响应式设计
- 企业级安全防护

## 许可证

本项目采用企业级商业许可证，仅供授权用户使用。

---

© 2024 Mika寄售平台. 企业级在线商城解决方案
