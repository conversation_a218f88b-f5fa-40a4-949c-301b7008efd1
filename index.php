<?php
// Mika寄售平台 - 企业级单页网站
// 配置信息
$API_BASE_URL = "http://127.0.0.1:7893/";

// 商户秘钥生成算法（与机器人保持一致）
function generate_merchant_secret($merchant_id) {
    $key = "yjsyjs_merchant_secret_key_2024";
    $salt = "yjsyjs_salt_2024";
    
    $combined = $merchant_id . $key . $salt;
    $hash_value = md5($combined);
    $prefix = substr($merchant_id, 0, 3);
    
    return $prefix . "_" . $hash_value;
}

// API调用函数
function call_api($endpoint, $params = []) {
    global $API_BASE_URL;
    
    $url = $API_BASE_URL . $endpoint;
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    if ($response === false) {
        return null;
    }
    
    return json_decode($response, true);
}

// 获取当前页面参数
$action = $_GET['action'] ?? 'home';
$merchant_id = $_GET['merchant_id'] ?? $_GET['merchant'] ?? ''; // 支持两种参数格式
$product_id = $_GET['product_id'] ?? '';
$order_id = $_GET['order_id'] ?? '';

// 如果有merchant参数，自动跳转到商城页面
if (!empty($merchant_id) && $action === 'home') {
    $action = 'shop';
}

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json; charset=utf-8');

    try {
        switch ($_POST['action']) {
            case 'register_merchant':
                if (empty($_POST['merchant_id'])) {
                    echo json_encode(['status' => 'error', 'message' => '商户ID不能为空']);
                    exit;
                }
                $result = call_api('register_merchant.php', ['merchant_id' => $_POST['merchant_id']]);
                echo json_encode($result ?: ['status' => 'error', 'message' => 'API调用失败']);
                exit;

            case 'get_merchant_info':
                if (empty($_POST['merchant_id'])) {
                    echo json_encode(['status' => 'error', 'message' => '商户ID不能为空']);
                    exit;
                }
                $result = call_api('get_merchant_info.php', ['merchant_id' => $_POST['merchant_id']]);
                echo json_encode($result ?: ['status' => 'error', 'message' => 'API调用失败']);
                exit;

            case 'get_product_list':
                if (empty($_POST['merchant_id'])) {
                    echo json_encode(['status' => 'error', 'message' => '商户ID不能为空']);
                    exit;
                }
                $merchant_secret = generate_merchant_secret($_POST['merchant_id']);
                $result = call_api('get_product_list.php', ['merchant_secret' => $merchant_secret]);
                echo json_encode($result ?: ['status' => 'error', 'message' => 'API调用失败']);
                exit;

            case 'get_product_info':
                if (empty($_POST['product_id'])) {
                    echo json_encode(['status' => 'error', 'message' => '商品ID不能为空']);
                    exit;
                }
                $result = call_api('get_product_info.php', ['product_id' => $_POST['product_id']]);
                echo json_encode($result ?: ['status' => 'error', 'message' => 'API调用失败']);
                exit;

            case 'create_order':
                if (empty($_POST['customer_contact']) || empty($_POST['product_id'])) {
                    echo json_encode(['status' => 'error', 'message' => '参数不完整']);
                    exit;
                }
                $result = call_api('create_order.php', [
                    'customer_contact' => $_POST['customer_contact'],
                    'product_id' => $_POST['product_id'],
                    'pay_type' => $_POST['pay_type'] ?? 'wxpay'
                ]);
                echo json_encode($result ?: ['status' => 'error', 'message' => 'API调用失败']);
                exit;

            case 'check_payment_status':
                if (empty($_POST['order_id'])) {
                    echo json_encode(['status' => 'error', 'message' => '订单ID不能为空']);
                    exit;
                }
                $result = call_api('check_payment_status.php', ['order_id' => $_POST['order_id']]);
                echo json_encode($result ?: ['status' => 'error', 'message' => 'API调用失败']);
                exit;

            default:
                echo json_encode(['status' => 'error', 'message' => '未知操作']);
                exit;
        }
    } catch (Exception $e) {
        echo json_encode(['status' => 'error', 'message' => '服务器内部错误: ' . $e->getMessage()]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mika寄售平台 - 企业级在线商城</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --dark-color: #34495e;
            --light-color: #ecf0f1;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1200px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }
        
        .nav-tabs {
            background: var(--light-color);
            border-bottom: none;
            padding: 0 2rem;
        }
        
        .nav-tabs .nav-link {
            border: none;
            color: var(--dark-color);
            font-weight: 600;
            padding: 1rem 1.5rem;
            margin-right: 0.5rem;
            border-radius: 10px 10px 0 0;
            transition: all 0.3s ease;
        }
        
        .nav-tabs .nav-link:hover {
            background: rgba(52, 152, 219, 0.1);
            color: var(--secondary-color);
        }
        
        .nav-tabs .nav-link.active {
            background: white;
            color: var(--primary-color);
            border-bottom: 3px solid var(--secondary-color);
        }
        
        .content-area {
            padding: 2rem;
            min-height: 500px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 1.5rem;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9, var(--secondary-color));
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #229954);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        .product-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--success-color);
        }
        
        .stock-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--success-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .stock-badge.out-of-stock {
            background: var(--danger-color);
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
        }
        
        .loading .spinner-border {
            width: 3rem;
            height: 3rem;
            color: var(--secondary-color);
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            padding: 1rem 1.5rem;
        }
        
        .footer {
            background: var(--dark-color);
            color: white;
            text-align: center;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content-area {
                padding: 1rem;
            }
            
            .nav-tabs {
                padding: 0 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="bi bi-shop"></i> Mika寄售平台</h1>
                <p>企业级在线商城 • 7x24h自动发货 • 安全可靠 • 零门槛入驻</p>
            </div>
            
            <!-- 导航标签 -->
            <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button" role="tab">
                        <i class="bi bi-house-door"></i> 首页
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="shop-tab" data-bs-toggle="tab" data-bs-target="#shop" type="button" role="tab">
                        <i class="bi bi-shop"></i> 商城
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="merchant-tab" data-bs-toggle="tab" data-bs-target="#merchant" type="button" role="tab">
                        <i class="bi bi-person-badge"></i> 商户中心
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="order-tab" data-bs-toggle="tab" data-bs-target="#order" type="button" role="tab">
                        <i class="bi bi-receipt"></i> 订单查询
                    </button>
                </li>
            </ul>
            
            <!-- 内容区域 -->
            <div class="tab-content content-area" id="mainTabContent">
                <!-- 首页 -->
                <div class="tab-pane fade show active" id="home" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-8 mx-auto text-center">
                            <h2 class="mb-4">欢迎来到Mika寄售平台</h2>
                            <p class="lead mb-4">这是一个匿名、安全的寄售平台，一站式托管发卡平台，零门槛入驻。</p>
                            
                            <div class="row g-4 mt-4">
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="bi bi-shield-check text-success" style="font-size: 3rem;"></i>
                                            <h5 class="card-title mt-3">安全可靠</h5>
                                            <p class="card-text">采用企业级安全防护，保障交易安全</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="bi bi-clock text-primary" style="font-size: 3rem;"></i>
                                            <h5 class="card-title mt-3">7x24h服务</h5>
                                            <p class="card-text">全天候自动发货，无需人工干预</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="bi bi-person-plus text-warning" style="font-size: 3rem;"></i>
                                            <h5 class="card-title mt-3">零门槛入驻</h5>
                                            <p class="card-text">简单注册即可开店，快速上架商品</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-5">
                                <button class="btn btn-primary btn-lg me-3" onclick="switchTab('merchant-tab')">
                                    <i class="bi bi-shop"></i> 立即开店
                                </button>
                                <button class="btn btn-success btn-lg" onclick="switchTab('shop-tab')">
                                    <i class="bi bi-bag-check"></i> 开始购物
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商城 -->
                <div class="tab-pane fade" id="shop" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-search"></i> 商户搜索</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="merchantSearch" class="form-label">输入商户ID</label>
                                        <input type="text" class="form-control" id="merchantSearch" placeholder="请输入商户ID">
                                    </div>
                                    <button class="btn btn-primary w-100" onclick="searchMerchant()">
                                        <i class="bi bi-search"></i> 搜索商户
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-8">
                            <div id="merchantInfo" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-shop"></i> 商户信息</h5>
                                    </div>
                                    <div class="card-body" id="merchantDetails">
                                        <!-- 商户信息将在这里显示 -->
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <h5><i class="bi bi-box-seam"></i> 商品列表</h5>
                                    <div id="productList" class="row">
                                        <!-- 商品列表将在这里显示 -->
                                    </div>
                                </div>
                            </div>

                            <div id="noMerchant" class="text-center">
                                <i class="bi bi-search text-muted" style="font-size: 4rem;"></i>
                                <h4 class="text-muted mt-3">请输入商户ID搜索商品</h4>
                                <p class="text-muted">输入有效的商户ID来浏览该商户的商品</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商户中心 -->
                <div class="tab-pane fade" id="merchant" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-6 mx-auto">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-person-badge"></i> 商户注册/登录</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="merchantId" class="form-label">商户ID</label>
                                        <input type="text" class="form-control" id="merchantId" placeholder="请输入您的商户ID">
                                        <div class="form-text">如果是新商户，系统将自动为您注册</div>
                                    </div>
                                    <button class="btn btn-primary w-100" onclick="loginMerchant()">
                                        <i class="bi bi-box-arrow-in-right"></i> 登录/注册
                                    </button>
                                </div>
                            </div>

                            <div id="merchantDashboard" style="display: none;" class="mt-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-speedometer2"></i> 商户后台</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="merchantDashboardContent">
                                            <!-- 商户后台内容将在这里显示 -->
                                        </div>
                                        <div class="mt-3">
                                            <a href="#" id="adminLink" class="btn btn-success" target="_blank">
                                                <i class="bi bi-gear"></i> 进入管理后台
                                            </a>
                                            <button class="btn btn-warning" onclick="shareMerchant()">
                                                <i class="bi bi-share"></i> 分享店铺
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单查询 -->
                <div class="tab-pane fade" id="order" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-6 mx-auto">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-receipt"></i> 订单查询</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="orderId" class="form-label">订单号</label>
                                        <input type="text" class="form-control" id="orderId" placeholder="请输入订单号">
                                    </div>
                                    <button class="btn btn-primary w-100" onclick="checkOrder()">
                                        <i class="bi bi-search"></i> 查询订单
                                    </button>
                                </div>
                            </div>

                            <div id="orderResult" style="display: none;" class="mt-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="bi bi-file-earmark-text"></i> 订单详情</h5>
                                    </div>
                                    <div class="card-body" id="orderDetails">
                                        <!-- 订单详情将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部 -->
            <div class="footer">
                <p>&copy; 2024 Mika寄售平台. 企业级在线商城解决方案</p>
                <p>
                    <a href="https://cloudshop.qnm6.top/tousu.html" class="text-light me-3">
                        <i class="bi bi-exclamation-triangle"></i> 投诉建议
                    </a>
                    <span class="text-light">官方频道: @MikaJiShou8</span>
                </p>
            </div>
        </div>
    </div>

    <!-- 商品详情模态框 -->
    <div class="modal fade" id="productModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-box-seam"></i> 商品详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="productModalBody">
                    <!-- 商品详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 购买模态框 -->
    <div class="modal fade" id="buyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-cart-plus"></i> 购买商品</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="customerContact" class="form-label">联系方式</label>
                        <input type="text" class="form-control" id="customerContact" placeholder="请输入您的联系方式（QQ/微信/邮箱等）">
                        <div class="form-text">用于接收商品和售后服务</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">支付方式</label>
                        <div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payType" id="wxpay" value="wxpay" checked>
                                <label class="form-check-label" for="wxpay">
                                    <i class="bi bi-wechat text-success"></i> 微信支付
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="payType" id="alipay" value="alipay">
                                <label class="form-check-label" for="alipay">
                                    <i class="bi bi-alipay text-primary"></i> 支付宝
                                </label>
                            </div>
                        </div>
                    </div>
                    <div id="productSummary" class="alert alert-info">
                        <!-- 商品摘要 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createOrder()">
                        <i class="bi bi-credit-card"></i> 立即购买
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 支付结果模态框 -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-credit-card"></i> 支付信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="paymentModalBody">
                    <!-- 支付信息内容 -->
                </div>
                <div class="modal-footer" id="paymentModalFooter">
                    <!-- 支付按钮 -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 全局变量
        let currentProductId = null;
        let currentMerchantId = null;
        let currentOrderId = null;

        // 切换标签页
        function switchTab(tabId) {
            const tab = new bootstrap.Tab(document.getElementById(tabId));
            tab.show();
        }

        // 显示加载状态
        function showLoading(elementId) {
            document.getElementById(elementId).innerHTML = `
                <div class="loading">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">加载中...</p>
                </div>
            `;
        }

        // 显示错误信息
        function showError(elementId, message) {
            document.getElementById(elementId).innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> ${message}
                </div>
            `;
        }

        // 显示成功信息
        function showSuccess(elementId, message) {
            document.getElementById(elementId).innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> ${message}
                </div>
            `;
        }

        // API调用函数
        async function callAPI(action, data) {
            try {
                const formData = new FormData();
                formData.append('ajax', '1');
                formData.append('action', action);

                for (const key in data) {
                    formData.append(key, data[key]);
                }

                const response = await fetch('', {
                    method: 'POST',
                    body: formData
                });

                return await response.json();
            } catch (error) {
                console.error('API调用失败:', error);
                return { status: 'error', message: '网络请求失败' };
            }
        }

        // 搜索商户
        async function searchMerchant() {
            const merchantId = document.getElementById('merchantSearch').value.trim();
            if (!merchantId) {
                alert('请输入商户ID');
                return;
            }

            showLoading('merchantDetails');
            document.getElementById('merchantInfo').style.display = 'block';
            document.getElementById('noMerchant').style.display = 'none';

            // 获取商户信息
            const merchantResult = await callAPI('get_merchant_info', { merchant_id: merchantId });

            if (merchantResult.status === 'success') {
                const merchantInfo = merchantResult.data;
                document.getElementById('merchantDetails').innerHTML = `
                    <h6><i class="bi bi-shop"></i> ${merchantInfo.shop_name || '未知商店'}</h6>
                    <p class="text-muted">${merchantInfo.shop_description || '暂无介绍'}</p>
                    <small class="text-muted">商户ID: ${merchantId}</small>
                `;

                // 获取商品列表
                loadProductList(merchantId);
            } else {
                showError('merchantDetails', merchantResult.message || '商户不存在');
                document.getElementById('productList').innerHTML = '';
            }
        }

        // 加载商品列表
        async function loadProductList(merchantId) {
            showLoading('productList');

            const result = await callAPI('get_product_list', { merchant_id: merchantId });

            if (result.status === 'success' && result.data.length > 0) {
                let html = '';
                result.data.forEach(product => {
                    const stockQuantity = parseInt(product.stock_quantity) || 0;
                    const isOutOfStock = stockQuantity <= 0;

                    html += `
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card product-card">
                                <div class="card-body position-relative">
                                    <span class="stock-badge ${isOutOfStock ? 'out-of-stock' : ''}">
                                        ${isOutOfStock ? '售罄' : `库存: ${stockQuantity}`}
                                    </span>
                                    <h6 class="card-title">${product.product_name}</h6>
                                    <p class="card-text text-muted small">${product.product_description || '暂无描述'}</p>
                                    <div class="product-price mb-3">¥${product.product_price}</div>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary btn-sm" onclick="showProductDetail('${product.product_id}')">
                                            <i class="bi bi-eye"></i> 查看详情
                                        </button>
                                        ${!isOutOfStock ? `
                                            <button class="btn btn-primary btn-sm" onclick="showBuyModal('${merchantId}', '${product.product_id}')">
                                                <i class="bi bi-cart-plus"></i> 立即购买
                                            </button>
                                        ` : `
                                            <button class="btn btn-secondary btn-sm" disabled>
                                                <i class="bi bi-x-circle"></i> 已售罄
                                            </button>
                                        `}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                document.getElementById('productList').innerHTML = html;
            } else {
                document.getElementById('productList').innerHTML = `
                    <div class="col-12 text-center">
                        <i class="bi bi-box text-muted" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-3">该商户还没有商品</h5>
                    </div>
                `;
            }
        }

        // 显示商品详情
        async function showProductDetail(productId) {
            const result = await callAPI('get_product_info', { product_id: productId });

            if (result.status === 'success') {
                const product = result.data;
                const stockQuantity = parseInt(product.stock_quantity) || 0;

                document.getElementById('productModalBody').innerHTML = `
                    <div class="row">
                        <div class="col-12">
                            <h5>${product.product_name}</h5>
                            <p class="text-muted">${product.product_description || '暂无描述'}</p>
                            <hr>
                            <div class="row">
                                <div class="col-sm-6">
                                    <strong>商品价格:</strong> <span class="text-success">¥${product.product_price}</span>
                                </div>
                                <div class="col-sm-6">
                                    <strong>库存数量:</strong> <span class="${stockQuantity > 0 ? 'text-success' : 'text-danger'}">${stockQuantity} 件</span>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-sm-6">
                                    <strong>商品编号:</strong> ${product.product_id}
                                </div>
                                <div class="col-sm-6">
                                    <strong>商户ID:</strong> ${product.merchant_id}
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                const modal = new bootstrap.Modal(document.getElementById('productModal'));
                modal.show();
            } else {
                alert('获取商品信息失败');
            }
        }

        // 显示购买模态框
        async function showBuyModal(merchantId, productId) {
            const result = await callAPI('get_product_info', { product_id: productId });

            if (result.status === 'success') {
                const product = result.data;
                currentProductId = productId;
                currentMerchantId = merchantId;

                document.getElementById('productSummary').innerHTML = `
                    <strong>商品:</strong> ${product.product_name}<br>
                    <strong>价格:</strong> <span class="text-success">¥${product.product_price}</span><br>
                    <strong>库存:</strong> ${product.stock_quantity} 件
                `;

                const modal = new bootstrap.Modal(document.getElementById('buyModal'));
                modal.show();
            } else {
                alert('获取商品信息失败');
            }
        }

        // 创建订单
        async function createOrder() {
            const customerContact = document.getElementById('customerContact').value.trim();
            const payType = document.querySelector('input[name="payType"]:checked').value;

            if (!customerContact) {
                alert('请输入联系方式');
                return;
            }

            if (!currentProductId) {
                alert('商品信息错误');
                return;
            }

            const result = await callAPI('create_order', {
                customer_contact: customerContact,
                product_id: currentProductId,
                pay_type: payType
            });

            if (result.status === 'success') {
                const orderData = result.data;
                const orderInfo = orderData.order_info;
                const paymentUrl = orderData.payment_url;

                currentOrderId = orderInfo.order_id;

                // 关闭购买模态框
                bootstrap.Modal.getInstance(document.getElementById('buyModal')).hide();

                // 显示支付信息
                showPaymentModal(orderInfo, paymentUrl);
            } else {
                alert(result.message || '创建订单失败');
            }
        }

        // 显示支付模态框
        function showPaymentModal(orderInfo, paymentUrl) {
            document.getElementById('paymentModalBody').innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="bi bi-check-circle"></i> 订单创建成功！</h6>
                </div>
                <div class="row">
                    <div class="col-sm-6"><strong>订单号:</strong></div>
                    <div class="col-sm-6">${orderInfo.order_id}</div>
                </div>
                <div class="row">
                    <div class="col-sm-6"><strong>商品名称:</strong></div>
                    <div class="col-sm-6">${orderInfo.product_name}</div>
                </div>
                <div class="row">
                    <div class="col-sm-6"><strong>支付金额:</strong></div>
                    <div class="col-sm-6 text-success"><strong>¥${orderInfo.product_price}</strong></div>
                </div>
                <div class="row">
                    <div class="col-sm-6"><strong>创建时间:</strong></div>
                    <div class="col-sm-6">${orderInfo.created_at}</div>
                </div>
                <hr>
                <div class="alert alert-warning">
                    <i class="bi bi-info-circle"></i> 请完成支付后点击"我已支付"按钮确认
                </div>
            `;

            document.getElementById('paymentModalFooter').innerHTML = `
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <a href="${paymentUrl}" class="btn btn-primary" target="_blank">
                    <i class="bi bi-credit-card"></i> 立即支付
                </a>
                <button type="button" class="btn btn-success" onclick="checkPaymentStatus('${orderInfo.order_id}')">
                    <i class="bi bi-check-circle"></i> 我已支付
                </button>
            `;

            const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
            modal.show();
        }

        // 检查支付状态
        async function checkPaymentStatus(orderId) {
            const result = await callAPI('check_payment_status', { order_id: orderId });

            if (result.status === 'success') {
                const orderData = result.data;

                if (orderData.order_status === 'paid') {
                    // 支付成功
                    document.getElementById('paymentModalBody').innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="bi bi-check-circle"></i> 支付成功！</h6>
                        </div>
                        <div class="row">
                            <div class="col-sm-6"><strong>订单号:</strong></div>
                            <div class="col-sm-6">${orderData.order_id}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6"><strong>商品名称:</strong></div>
                            <div class="col-sm-6">${orderData.product_name}</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6"><strong>支付金额:</strong></div>
                            <div class="col-sm-6 text-success"><strong>¥${orderData.product_price}</strong></div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6"><strong>支付时间:</strong></div>
                            <div class="col-sm-6">${orderData.updated_at}</div>
                        </div>
                        <hr>
                        <div class="alert alert-info">
                            <h6><i class="bi bi-gift"></i> 发货内容:</h6>
                            <pre class="mb-0">${orderData.delivery_content}</pre>
                        </div>
                    `;

                    document.getElementById('paymentModalFooter').innerHTML = `
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <a href="https://cloudshop.qnm6.top/tousu.html" class="btn btn-warning" target="_blank">
                            <i class="bi bi-exclamation-triangle"></i> 投诉此订单
                        </a>
                    `;
                } else {
                    alert('订单尚未支付，请完成支付后再试');
                }
            } else {
                alert('检查支付状态失败');
            }
        }

        // 商户登录/注册
        async function loginMerchant() {
            const merchantId = document.getElementById('merchantId').value.trim();
            if (!merchantId) {
                alert('请输入商户ID');
                return;
            }

            // 先尝试注册商户（如果已存在会忽略）
            await callAPI('register_merchant', { merchant_id: merchantId });

            // 获取商户信息
            const result = await callAPI('get_merchant_info', { merchant_id: merchantId });

            if (result.status === 'success') {
                const merchantInfo = result.data;

                document.getElementById('merchantDashboardContent').innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> 登录成功！欢迎回来
                    </div>
                    <div class="row">
                        <div class="col-sm-6"><strong>商户ID:</strong></div>
                        <div class="col-sm-6">${merchantId}</div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6"><strong>店铺名称:</strong></div>
                        <div class="col-sm-6">${merchantInfo.shop_name || '未设置'}</div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6"><strong>店铺介绍:</strong></div>
                        <div class="col-sm-6">${merchantInfo.shop_description || '未设置'}</div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6"><strong>注册时间:</strong></div>
                        <div class="col-sm-6">${merchantInfo.created_at || '未知'}</div>
                    </div>
                `;

                // 生成管理后台链接
                const adminUrl = generateAdminUrl(merchantId);
                document.getElementById('adminLink').href = adminUrl;

                document.getElementById('merchantDashboard').style.display = 'block';
            } else {
                alert(result.message || '登录失败');
            }
        }

        // 生成管理后台URL
        function generateAdminUrl(merchantId) {
            // 使用与机器人相同的算法生成商户秘钥
            const key = "yjsyjs_merchant_secret_key_2024";
            const salt = "yjsyjs_salt_2024";
            const combined = merchantId + key + salt;

            // 简单的MD5实现（实际应用中建议使用专业的加密库）
            const merchantSecret = merchantId.substring(0, 3) + "_" + btoa(combined).substring(0, 32);

            return `https://cloudshop.qnm6.top/admin/index.php?merchant_secret=${merchantSecret}`;
        }

        // 分享商户
        function shareMerchant() {
            const merchantId = document.getElementById('merchantId').value.trim();
            if (!merchantId) {
                alert('请先登录');
                return;
            }

            const shareUrl = `${window.location.origin}${window.location.pathname}?action=shop&merchant_id=${merchantId}`;
            const shareText = `💎 这是我的在线商城!7x24h自动发货、售后稳定,交易完全匿名,欢迎下单！\n\n🔗 店铺链接：${shareUrl}`;

            // 尝试使用Web Share API
            if (navigator.share) {
                navigator.share({
                    title: 'Mika寄售平台 - 我的店铺',
                    text: shareText,
                    url: shareUrl
                });
            } else {
                // 复制到剪贴板
                navigator.clipboard.writeText(shareText).then(() => {
                    alert('分享链接已复制到剪贴板！');
                }).catch(() => {
                    // 显示分享内容
                    prompt('请复制以下分享内容:', shareText);
                });
            }
        }

        // 查询订单
        async function checkOrder() {
            const orderId = document.getElementById('orderId').value.trim();
            if (!orderId) {
                alert('请输入订单号');
                return;
            }

            showLoading('orderDetails');
            document.getElementById('orderResult').style.display = 'block';

            const result = await callAPI('check_payment_status', { order_id: orderId });

            if (result.status === 'success') {
                const orderData = result.data;

                let statusBadge = '';
                let statusText = '';
                let deliveryContent = '';

                if (orderData.order_status === 'paid') {
                    statusBadge = '<span class="badge bg-success">已支付</span>';
                    statusText = '订单已支付并自动发货';
                    deliveryContent = `
                        <hr>
                        <div class="alert alert-info">
                            <h6><i class="bi bi-gift"></i> 发货内容:</h6>
                            <pre class="mb-0">${orderData.delivery_content}</pre>
                        </div>
                        <div class="mt-3">
                            <a href="https://cloudshop.qnm6.top/tousu.html" class="btn btn-warning" target="_blank">
                                <i class="bi bi-exclamation-triangle"></i> 投诉此订单
                            </a>
                        </div>
                    `;
                } else {
                    statusBadge = '<span class="badge bg-warning">未支付</span>';
                    statusText = '订单尚未支付';
                }

                document.getElementById('orderDetails').innerHTML = `
                    <div class="row">
                        <div class="col-sm-6"><strong>订单状态:</strong></div>
                        <div class="col-sm-6">${statusBadge}</div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6"><strong>订单号:</strong></div>
                        <div class="col-sm-6">${orderData.order_id}</div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6"><strong>商户ID:</strong></div>
                        <div class="col-sm-6">${orderData.merchant_id}</div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6"><strong>商品名称:</strong></div>
                        <div class="col-sm-6">${orderData.product_name}</div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6"><strong>订单金额:</strong></div>
                        <div class="col-sm-6 text-success"><strong>¥${orderData.product_price}</strong></div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6"><strong>下单时间:</strong></div>
                        <div class="col-sm-6">${orderData.created_at}</div>
                    </div>
                    ${orderData.updated_at ? `
                        <div class="row">
                            <div class="col-sm-6"><strong>支付时间:</strong></div>
                            <div class="col-sm-6">${orderData.updated_at}</div>
                        </div>
                    ` : ''}
                    <div class="row">
                        <div class="col-sm-6"><strong>购买人ID:</strong></div>
                        <div class="col-sm-6">${orderData.customer_contact}</div>
                    </div>
                    <hr>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> ${statusText}
                    </div>
                    ${deliveryContent}
                `;
            } else {
                showError('orderDetails', result.message || '订单不存在');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const action = urlParams.get('action');
            const merchantId = urlParams.get('merchant_id') || urlParams.get('merchant');
            const productId = urlParams.get('product_id');

            // 如果有merchant参数，自动跳转到商城
            if (merchantId) {
                switchTab('shop-tab');
                document.getElementById('merchantSearch').value = merchantId;
                setTimeout(() => searchMerchant(), 500);
            } else if (action === 'shop' && merchantId) {
                // 直接跳转到商城并搜索商户
                switchTab('shop-tab');
                document.getElementById('merchantSearch').value = merchantId;
                setTimeout(() => searchMerchant(), 500);
            } else if (action === 'product' && productId) {
                // 显示商品详情
                setTimeout(() => showProductDetail(productId), 500);
            }

            // 显示加载完成信息
            console.log('Mika寄售平台已加载完成');
            console.log('URL参数:', {
                action: action,
                merchant_id: merchantId,
                product_id: productId
            });
        });
    </script>
</body>
</html>
